package com.cheche365.bc.sharding;

import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AutoTask分表路由器
 * 基于startTime按周分表的路由
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AutoTaskShardingRouter {

    private static final String TABLE_PREFIX = "auto_task";

    private static final DateTimeFormatter WEEK_FORMATTER = DateTimeFormatter.ofPattern("yyyy_ww");

    private static final WeekFields WEEK_FIELDS = WeekFields.ISO;

    private final Map<String, Boolean> tableExistsCache = new ConcurrentHashMap<>();

    /**
     * 根据时间生成分表名
     *
     * @param dateTime
     * @return
     */
    public String getTableName(LocalDateTime dateTime) {
        if (dateTime == null) {
            return getTableName(LocalDateTime.now());
        }
        LocalDate date = dateTime.toLocalDate();
        int year = date.getYear();
        int week = date.get(WEEK_FIELDS.weekOfYear());
        return String.format("%s_%d_%02d", TABLE_PREFIX, year, week);
    }

    public List<String> getTableNames(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null && endTime == null) {
            return getAllRecentTableNames();
        }
        if (startTime == null) {
            startTime = LocalDateTime.now().minusMonths(3);
        }
        if (endTime == null) {
            endTime = LocalDateTime.now();
        }
        Set<String> tableNames = Sets.newLinkedHashSet();
        LocalDate currentDate = startTime.toLocalDate();
        LocalDate endDate = endTime.toLocalDate();
        while (!currentDate.isAfter(endDate)) {
            String tableName = getTableName(currentDate.atStartOfDay());
            tableNames.add(tableName);
            currentDate = currentDate.plusWeeks(1);
        }
        return Lists.newArrayList(tableNames);
    }

    private List<String> getAllRecentTableNames() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threeMonthsAgo = now.minusMonths(3);
        return getTableNames(threeMonthsAgo, now);
    }


    public static void main(String[] args) {
        AutoTaskShardingRouter router = new AutoTaskShardingRouter();
        System.out.println(router.getTableName(LocalDateTime.now()));
    }
}
